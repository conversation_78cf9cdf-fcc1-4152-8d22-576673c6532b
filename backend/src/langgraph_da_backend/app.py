# mypy: disable-error-code="no-untyped-def,misc"
import asyncio
import json
import os
import pathlib
import uuid
from typing import Any, Dict, List

from fastapi import (
    FastAPI,
    HTTPException,
    Response,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

# Import our graph
from .graph import graph

# Define the FastAPI app
app = FastAPI()

# Add CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic models
class ThreadCreate(BaseModel):
    pass


class RunCreate(BaseModel):
    assistant_id: str
    input: Dict[str, Any]
    config: Dict[str, Any] = {}


class ChatMessage(BaseModel):
    role: str
    content: str
    id: str = None


class ChatRequest(BaseModel):
    message: str
    file_path: str = None
    thread_id: str = None


# Store active WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.thread_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, thread_id: str = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if thread_id:
            if thread_id not in self.thread_connections:
                self.thread_connections[thread_id] = []
            self.thread_connections[thread_id].append(websocket)

    def disconnect(self, websocket: WebSocket, thread_id: str = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if thread_id and thread_id in self.thread_connections:
            if websocket in self.thread_connections[thread_id]:
                self.thread_connections[thread_id].remove(websocket)
            if not self.thread_connections[thread_id]:
                del self.thread_connections[thread_id]

    async def send_to_thread(self, thread_id: str, message: dict):
        if thread_id in self.thread_connections:
            for connection in self.thread_connections[thread_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    # Remove dead connections
                    self.disconnect(connection, thread_id)


manager = ConnectionManager()


# API endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "message": "Data Analysis LangGraph backend is running",
    }


@app.get("/assistants")
async def list_assistants():
    """List available assistants."""
    return [{"assistant_id": "agent", "name": "Data Analysis Agent"}]


@app.post("/threads")
async def create_thread():
    """Create a new thread."""
    thread_id = str(uuid.uuid4())
    return {"thread_id": thread_id}


@app.get("/threads")
async def list_threads():
    """List threads."""
    return []


@app.post("/threads/{thread_id}/runs")
async def create_run(thread_id: str, run_data: RunCreate):
    """Create and execute a run."""
    try:
        config = {"configurable": {"thread_id": thread_id}}
        config.update(run_data.config)

        # Execute the graph
        result = graph.invoke(run_data.input, config=config)

        run_id = str(uuid.uuid4())
        return {
            "run_id": run_id,
            "thread_id": thread_id,
            "assistant_id": run_data.assistant_id,
            "status": "completed",
            "output": result,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/threads/{thread_id}/runs/stream")
async def stream_run(thread_id: str, run_data: RunCreate):
    """Stream a run execution."""
    try:
        config = {"configurable": {"thread_id": thread_id}}
        config.update(run_data.config)

        def generate_stream():
            try:
                run_id = str(uuid.uuid4())

                # Send metadata
                yield f"data: {json.dumps({'event': 'metadata', 'data': {'run_id': run_id, 'thread_id': thread_id}})}\n\n"

                # Stream the graph execution
                for chunk in graph.stream(
                    run_data.input, config=config, stream_mode="values"
                ):
                    event_data = {"event": "values", "data": chunk}
                    yield f"data: {json.dumps(event_data)}\n\n"

                # Send completion
                yield f"data: {json.dumps({'event': 'end', 'data': {'run_id': run_id, 'status': 'completed'}})}\n\n"

            except Exception as e:
                error_data = {"event": "error", "data": {"error": str(e)}}
                yield f"data: {json.dumps(error_data)}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            },
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws/{thread_id}")
async def websocket_endpoint(websocket: WebSocket, thread_id: str):
    """WebSocket endpoint for real-time communication."""
    await manager.connect(websocket, thread_id)
    try:
        while True:
            # Wait for messages from the client
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # Handle different message types
            if message_data.get("type") == "chat":
                await handle_chat_message(thread_id, message_data, websocket)
            elif message_data.get("type") == "get_history":
                await send_history(thread_id, websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket, thread_id)
    except Exception as e:
        print(f"WebSocket error: {e}")
        manager.disconnect(websocket, thread_id)


async def handle_chat_message(
    thread_id: str, message_data: dict, websocket: WebSocket
):
    """Handle incoming chat messages and stream responses."""
    try:
        # Send acknowledgment
        await websocket.send_text(
            json.dumps(
                {
                    "type": "status",
                    "status": "processing",
                    "message": "Processing your request...",
                }
            )
        )

        # Check for required environment variables
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "error",
                        "error": "OPENAI_API_KEY environment variable is not set. Please set your OpenAI API key to use this application.",
                        "error_type": "ConfigurationError",
                        "message": "Missing API key configuration",
                    }
                )
            )
            return

        # Prepare the input for the graph
        user_message = message_data.get("message", "")
        file_path = message_data.get("file_path")

        print(f"💬 Processing message: {user_message}")
        print(f"📁 File path: {file_path}")

        config = {"configurable": {"thread_id": thread_id}}

        # Create the input based on your graph's expected format
        # Include all required fields from AgentState
        graph_input = {
            "current_user_prompt": user_message,
            "target_data_sources": [file_path] if file_path else [],
            "terminate_signal": False,
            "loaded_data_paths": [],
            "refined_data_path": None,
            "renewed_data_path": None,
            "abstract_plan_desc": "",
            "abstract_plan_feedback": "",
            "reflection_hint": "",
            "tool_exec_results": [],
            "execution_reflection_feedback": "",
            "summary_report": None,
        }

        # Send user message to client
        await websocket.send_text(
            json.dumps(
                {
                    "type": "message",
                    "role": "user",
                    "content": user_message,
                    "id": str(uuid.uuid4()),
                }
            )
        )

        # Stream the graph execution
        ai_message_id = str(uuid.uuid4())
        ai_content = ""

        try:
            # Send initial status
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "status",
                        "status": "starting",
                        "message": "Starting graph execution...",
                    }
                )
            )

            chunk_count = 0
            print(f"🚀 Starting graph execution with input: {graph_input}")

            for chunk in graph.stream(
                graph_input, config=config, stream_mode="values"
            ):
                chunk_count += 1
                print(
                    f"📊 Processing chunk {chunk_count}: {list(chunk.keys())}"
                )

                # Log more details about the chunk
                for key, value in chunk.items():
                    if key == "messages":
                        print(
                            f"  📝 Messages: {len(value) if value else 0} messages"
                        )
                    elif key in ["loaded_data_paths", "target_data_sources"]:
                        print(f"  📁 {key}: {value}")
                    elif key == "terminate_signal":
                        print(f"  🛑 Terminate signal: {value}")
                    elif key == "current_user_prompt":
                        print(
                            f"  💬 User prompt: {value[:50]}..."
                            if len(str(value)) > 50
                            else f"  💬 User prompt: {value}"
                        )
                    else:
                        print(
                            f"  🔧 {key}: {str(value)[:100]}..."
                            if len(str(value)) > 100
                            else f"  🔧 {key}: {value}"
                        )

                # Send progress updates (serialize LangChain objects)
                serialized_chunk = {}
                for key, value in chunk.items():
                    if key == "messages":
                        # Convert LangChain messages to serializable format
                        serialized_chunk[key] = [
                            {
                                "role": (
                                    msg.type
                                    if hasattr(msg, "type")
                                    else "unknown"
                                ),
                                "content": (
                                    msg.content
                                    if hasattr(msg, "content")
                                    else str(msg)
                                ),
                                "id": str(uuid.uuid4()),
                            }
                            for msg in value
                        ]
                    else:
                        # For other fields, try to serialize or convert to string
                        try:
                            json.dumps(value)  # Test if it's JSON serializable
                            serialized_chunk[key] = value
                        except (TypeError, ValueError):
                            serialized_chunk[key] = str(value)

                # Send progress update
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "progress",
                            "data": serialized_chunk,
                            "chunk_number": chunk_count,
                        }
                    )
                )

                # If there are messages in the chunk, extract AI responses
                if "messages" in chunk:
                    messages = chunk["messages"]
                    for msg in messages:
                        if hasattr(msg, "type") and msg.type == "ai":
                            ai_content = msg.content

            print(f"✅ Graph execution completed after {chunk_count} chunks")

            # Send final AI message if we have one
            if ai_content:
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "message",
                            "role": "ai",
                            "content": ai_content,
                            "id": ai_message_id,
                        }
                    )
                )

            # Send completion status
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "status",
                        "status": "completed",
                        "message": f"Analysis completed successfully after {chunk_count} steps",
                    }
                )
            )

        except Exception as e:
            error_msg = str(e)
            error_type = type(e).__name__
            print(f"❌ Graph execution error ({error_type}): {error_msg}")

            # Print full traceback for debugging
            import traceback

            print(f"📋 Full traceback:")
            traceback.print_exc()

            # Send detailed error information
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "error",
                        "error": error_msg,
                        "error_type": error_type,
                        "message": f"Graph execution failed: {error_type}",
                        "details": f"Error occurred during graph execution. Check backend logs for details.",
                    }
                )
            )

    except Exception as e:
        await websocket.send_text(
            json.dumps(
                {
                    "type": "error",
                    "error": f"Failed to process message: {str(e)}",
                }
            )
        )


async def send_history(thread_id: str, websocket: WebSocket):
    """Send conversation history to the client."""
    try:
        config = {"configurable": {"thread_id": thread_id}}
        state = graph.get_state(config)

        messages = []
        if state and state.values and "messages" in state.values:
            for msg in state.values["messages"]:
                if hasattr(msg, "type") and hasattr(msg, "content"):
                    # Convert LangChain message type to our format
                    role = "user" if msg.type == "human" else "ai"
                    messages.append(
                        {
                            "role": role,
                            "content": msg.content,
                            "id": str(uuid.uuid4()),
                        }
                    )

        await websocket.send_text(
            json.dumps({"type": "history", "messages": messages})
        )

    except Exception as e:
        await websocket.send_text(
            json.dumps(
                {"type": "error", "error": f"Failed to get history: {str(e)}"}
            )
        )


@app.post("/threads/{thread_id}/history")
async def get_thread_history(thread_id: str):
    """Get the history of a thread (REST endpoint for compatibility)."""
    try:
        config = {"configurable": {"thread_id": thread_id}}
        state = graph.get_state(config)

        messages = []
        if state and state.values and "messages" in state.values:
            for msg in state.values["messages"]:
                if hasattr(msg, "type") and hasattr(msg, "content"):
                    # Convert LangChain message type to our format
                    role = "user" if msg.type == "human" else "ai"
                    messages.append(
                        {
                            "role": role,
                            "content": msg.content,
                            "id": str(uuid.uuid4()),
                        }
                    )

        return messages

    except Exception as e:
        print(f"Warning: Could not get state for thread {thread_id}: {e}")
        return []


def create_frontend_router(build_dir="../../../frontend/dist"):
    """Creates a router to serve the React frontend.

    Args:
        build_dir: Path to the React build directory relative to this file.

    Returns:
        A Starlette application serving the frontend.
    """
    build_path = pathlib.Path(__file__).parent.parent.parent / build_dir

    if not build_path.is_dir() or not (build_path / "index.html").is_file():
        print(
            f"WARN: Frontend build directory not found or incomplete at {build_path}. Serving frontend will likely fail."
        )
        # Return a dummy router if build isn't ready
        from starlette.routing import Route

        async def dummy_frontend(_):
            return Response(
                "Frontend not built. Run 'npm run build' in the frontend directory.",
                media_type="text/plain",
                status_code=503,
            )

        return Route("/{path:path}", endpoint=dummy_frontend)

    return StaticFiles(directory=build_path, html=True)


# Mount the frontend under /app to not conflict with the LangGraph API routes
app.mount(
    "/app",
    create_frontend_router(),
    name="frontend",
)
