import { useState, useEffect, useRef, useCallback } from "react";
import { ProcessedEvent } from "@/components/ActivityTimeline";
import { WelcomeScreen } from "@/components/WelcomeScreen";
import { ChatMessagesView } from "@/components/ChatMessagesView";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Button } from "@/components/ui/button";
import { BarChart3 } from "lucide-react";
import { useWebSocketChat, type ChatMessage, type ProgressEvent } from "@/hooks/useWebSocketChat";



export default function App() {
  const [processedEventsTimeline, setProcessedEventsTimeline] = useState<
    ProcessedEvent[]
  >([]);
  const [historicalActivities, setHistoricalActivities] = useState<
    Record<string, ProcessedEvent[]>
  >({});
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const hasFinalizeEventOccurredRef = useRef(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'connecting' | 'connected' | 'failed'>('idle');
  const [statusMessage, setStatusMessage] = useState<string | null>(null);

  // Function to test backend connection
  const testBackendConnection = async (): Promise<boolean> => {
    try {
      const response = await fetch('http://localhost:2024/health', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      return response.ok;
    } catch (error) {
      console.error('Backend connection test failed:', error);
      return false;
    }
  };

  const chat = useWebSocketChat({
    apiUrl: import.meta.env.DEV
      ? "http://localhost:2024"
      : "http://localhost:2024",
    onStatus: (status: string, message?: string) => {
      console.log('📊 Status update:', status, message);
      setStatusMessage(message || status);
      if (status === 'completed') {
        setStatusMessage(null);
      }
    },
    onError: (error: Error) => {
      console.error('❌ Chat error:', error);
      setError(error.message);
      setStatusMessage(null);
    },
    onProgress: (event: ProgressEvent) => {
      let processedEvent: ProcessedEvent | null = null;
      const data = event.data;

      // Map backend workflow events to frontend timeline events
      if (data.standby) {
        processedEvent = {
          title: "Loading Data",
          data: "Preparing data sources and initializing workflow",
          status: "running",
        };
      } else if (data.abstract_plan) {
        processedEvent = {
          title: "Planning Analysis",
          data: "Creating analysis plan and understanding data requirements",
          status: "running",
        };
      } else if (data.data_engineering) {
        processedEvent = {
          title: "Data Engineering",
          data: "Cleaning, processing, and preparing data for analysis",
          status: "running",
        };
      } else if (data.data_analysis) {
        processedEvent = {
          title: "Data Analysis",
          data: "Performing analysis and generating insights",
          status: "running",
        };
        hasFinalizeEventOccurredRef.current = true;
      }

      if (processedEvent) {
        setProcessedEventsTimeline((prevEvents) => [
          ...prevEvents,
          processedEvent!,
        ]);
      }
    },
    onError: (error: Error) => {
      console.error("WebSocket Error:", error);
      setConnectionStatus('failed');
      setIsConnecting(false);

      // Provide specific error messages based on the error type
      if (error.message?.includes('Failed to fetch') || error.message?.includes('ERR_CONNECTION_REFUSED')) {
        setError('Cannot connect to the backend server. Please make sure the backend is running on http://localhost:2024');
      } else if (error.message?.includes('404')) {
        setError('Backend endpoint not found. Please check if the backend is properly configured.');
      } else if (error.message?.includes('500')) {
        setError('Backend server error. Please check the backend logs for more details.');
      } else {
        setError(`Connection failed: ${error.message || 'Unknown error occurred'}`);
      }
    },
  });

  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollViewport = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (scrollViewport) {
        scrollViewport.scrollTop = scrollViewport.scrollHeight;
      }
    }
  }, [chat.messages]);

  useEffect(() => {
    if (
      hasFinalizeEventOccurredRef.current &&
      !chat.isLoading &&
      chat.messages.length > 0
    ) {
      const lastMessage = chat.messages[chat.messages.length - 1];
      if (lastMessage && lastMessage.role === "ai" && lastMessage.id) {
        setHistoricalActivities((prev) => ({
          ...prev,
          [lastMessage.id!]: [...processedEventsTimeline],
        }));
      }
      hasFinalizeEventOccurredRef.current = false;
    }
  }, [chat.messages, chat.isLoading, processedEventsTimeline]);

  const handleSubmit = useCallback(
    async (query: string, filePath: string | null) => {
      if (!query.trim()) {
        setError("Please enter a question about your data");
        return;
      }

      if (!filePath) {
        setError("Please select a CSV file to analyze");
        return;
      }

      setProcessedEventsTimeline([]);
      hasFinalizeEventOccurredRef.current = false;
      setError(null);
      setIsConnecting(true);
      setConnectionStatus('connecting');

      // Test backend connection first
      const isBackendAvailable = await testBackendConnection();
      if (!isBackendAvailable) {
        setError("Cannot connect to the backend server. Please make sure the backend is running on http://localhost:2024");
        setIsConnecting(false);
        setConnectionStatus('failed');
        return;
      }

      // Generate a thread ID if we don't have one
      const threadId = Date.now().toString();

      try {
        // Connect to WebSocket
        chat.connect(threadId);
        setConnectionStatus('connected');

        // Send the message
        chat.sendMessage(query, filePath);

        // Add a timeout to detect if the backend is not responding
        setTimeout(() => {
          if (isConnecting && !chat.isLoading) {
            setError("Backend server is not responding. Please check if the backend is running on http://localhost:2024");
            setIsConnecting(false);
            setConnectionStatus('failed');
          }
        }, 10000); // Increased timeout to 10 seconds
      } catch (err: any) {
        setError(`Failed to submit request: ${err.message}`);
        setIsConnecting(false);
        setConnectionStatus('failed');
      }
    },
    [chat, isConnecting, testBackendConnection]
  );

  const handleCancel = useCallback(() => {
    chat.disconnect();
    setProcessedEventsTimeline([]);
    setError(null);
    setIsConnecting(false);
    setConnectionStatus('idle');
  }, [chat]);

  const handleRestart = useCallback(() => {
    setProcessedEventsTimeline([]);
    setError(null);
    setIsConnecting(false);
    window.location.reload();
  }, []);

  return (
    <div className="flex flex-col h-screen w-full bg-white text-gray-900 font-sans antialiased dark:bg-gray-900 dark:text-gray-100">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:border-gray-700 dark:bg-gray-900/95 dark:supports-[backdrop-filter]:bg-gray-900/60 flex-shrink-0">
        <div className="flex h-12 items-center justify-between px-4 w-full max-w-7xl mx-auto">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-500" />
            <h1 className="text-base font-semibold hidden sm:block">Data Analysis Assistant</h1>
            <h1 className="text-base font-semibold sm:hidden">DA Assistant</h1>

            {/* Connection Status Indicator */}
            {connectionStatus !== 'idle' && (
              <div className="flex items-center gap-1 text-xs">
                {connectionStatus === 'connecting' && (
                  <>
                    <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                    <span className="text-yellow-600 dark:text-yellow-400 hidden sm:inline">Connecting...</span>
                  </>
                )}
                {connectionStatus === 'connected' && (
                  <>
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-green-600 dark:text-green-400 hidden sm:inline">Connected</span>
                  </>
                )}
                {connectionStatus === 'failed' && (
                  <>
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-red-600 dark:text-red-400 hidden sm:inline">Disconnected</span>
                  </>
                )}
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <ThemeToggle />
            {chat.messages.length > 0 && (
              <Button variant="outline" size="sm" onClick={handleRestart}>
                <span className="hidden sm:inline">New Analysis</span>
                <span className="sm:hidden">New</span>
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 w-full max-w-7xl mx-auto border-x border-gray-200 overflow-hidden dark:border-gray-700 min-h-0">
        {chat.messages.length === 0 ? (
          <WelcomeScreen
            handleSubmit={handleSubmit}
            isLoading={chat.isLoading || isConnecting}
            onCancel={handleCancel}
          />
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-full p-4 sm:p-8">
            <div className="flex flex-col items-center justify-center gap-4 max-w-lg text-center">
              <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-2">
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">Connection Error</h1>
              <p className="text-red-600 dark:text-red-400 text-sm leading-relaxed">{error}</p>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mt-4">
                <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Troubleshooting:</h3>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 text-left space-y-1">
                  <li>• Make sure the LangGraph backend is running on port 2024</li>
                  <li>• Check backend status: <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">python test_langgraph_studio.py</code></li>
                  <li>• Verify the backend is accessible at <a href="http://localhost:2024" target="_blank" rel="noopener noreferrer" className="underline">http://localhost:2024</a></li>
                  <li>• Try starting the backend with: <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">langgraph dev</code></li>
                  <li>• Check browser console (F12) for detailed error messages</li>
                </ul>
              </div>

              <div className="flex gap-2 mt-4">
                <Button variant="destructive" onClick={handleRestart}>
                  Restart Application
                </Button>
                <Button variant="outline" onClick={() => setError(null)}>
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <>
            {statusMessage && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4 mx-4 mt-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-4 h-4 bg-blue-400 rounded-full animate-pulse"></div>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-700 dark:text-blue-300">{statusMessage}</p>
                  </div>
                </div>
              </div>
            )}
            <ChatMessagesView
              messages={chat.messages}
              isLoading={chat.isLoading}
              scrollAreaRef={scrollAreaRef}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              liveActivityEvents={processedEventsTimeline}
              historicalActivities={historicalActivities}
            />
          </>
        )}
      </main>
    </div>
  );
}
