# OpenAI API Key (required for LLM functionality)
OPENAI_API_KEY=your_openai_api_key_here

# <PERSON><PERSON><PERSON> (optional, for tracing and monitoring)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=data-analysis-langgraph

# Server configuration
HOST=0.0.0.0
PORT=8000

# Data directory (where CSV files are stored)
DATA_DIR=./data

# Cache directory
CACHE_DIR=./.file_cache

# Checkpoint database
CHECKPOINT_DB_PATH=./.checkpoints/checkpoints.db
